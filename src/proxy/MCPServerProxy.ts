/**
 * MCP Server Proxy - Main proxy server implementation
 */

import { EventEmitter } from 'events';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  ListToolsRequestSchema,
  CallToolRequestSchema,
  ListResourcesRequestSchema,
  ListResourceTemplatesRequestSchema,
  ReadResourceRequestSchema,
  ListPromptsRequestSchema,
  GetPromptRequestSchema
} from '@modelcontextprotocol/sdk/types.js';
import { z } from 'zod';

import { BackendManager } from '../managers/BackendManager.js';
import { CapabilityAggregator } from '../managers/CapabilityAggregator.js';
import { RequestRouter } from '../managers/RequestRouter.js';
import { NotificationManager } from '../managers/NotificationManager.js';
import { ProxyConfig } from '../types/index.js';
import { logger } from '../utils/logger.js';

/**
 * Proxy server state
 */
interface ProxyState {
  initialized: boolean;
  clientCapabilities?: any;
}

/**
 * Main MCP Server Proxy class
 */
export class MCPServerProxy extends EventEmitter {
  private server: Server;
  private backendManager: BackendManager;
  private capabilityAggregator: CapabilityAggregator;
  private requestRouter: RequestRouter;
  private notificationManager: NotificationManager;
  private config: ProxyConfig;
  private state: ProxyState;

  constructor(config: ProxyConfig) {
    super();

    this.config = config;
    this.state = {
      initialized: false
    };

    // Initialize MCP server with static capabilities
    // Note: MCP SDK requires capabilities to be declared at initialization
    this.server = new Server(
      {
        name: config.serverInfo.name,
        version: config.serverInfo.version
      },
      {
        capabilities: {
          tools: {
            listChanged: true
          },
          resources: {
            subscribe: true,
            listChanged: true
          },
          prompts: {
            listChanged: true
          }
        }
      }
    );

    // Initialize managers
    this.backendManager = new BackendManager();
    this.capabilityAggregator = new CapabilityAggregator();
    this.requestRouter = new RequestRouter(this.backendManager, this.capabilityAggregator);
    this.notificationManager = new NotificationManager(this.backendManager);

    this.setupEventHandlers();
    this.setupServerHandlers();
  }

  /**
   * Set up event handlers between managers
   */
  private setupEventHandlers(): void {
    // Backend manager events
    this.backendManager.on('server-connected', (serverId: string) => {
      logger.info(`Backend server connected: ${serverId}`);
      this.updateAggregatedCapabilities();
    });

    this.backendManager.on('server-disconnected', (serverId: string) => {
      logger.info(`Backend server disconnected: ${serverId}`);
      this.updateAggregatedCapabilities();
    });

    this.backendManager.on('capabilities-updated', (serverId: string) => {
      logger.debug(`Capabilities updated for server: ${serverId}`);
      this.updateAggregatedCapabilities();
    });

    // Capability aggregator events
    this.capabilityAggregator.on('capabilitiesChanged', () => {
      logger.debug('Capabilities changed, notifying clients');
      // Send notifications to clients about capability changes
      this.sendCapabilityChangeNotifications();
    });

    // Notification manager events
    this.notificationManager.on('notification', (notification: any) => {
      this.forwardNotification(notification);
    });
  }

  /**
   * Set up MCP server handlers using the low-level API
   */
  private setupServerHandlers(): void {
    // Set up all required MCP request handlers
    this.setupRequestHandlers();
  }

  /**
   * Set up all MCP request handlers
   */
  private setupRequestHandlers(): void {
    // Tools handlers
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      const capabilities = this.capabilityAggregator.getCapabilities();
      return { tools: capabilities.tools };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      // Convert to JSONRPCRequest format for router
      const jsonRpcRequest = {
        jsonrpc: '2.0' as const,
        id: Math.random().toString(36),
        method: 'tools/call',
        params: request.params
      };
      const response = await this.requestRouter.routeRequest(jsonRpcRequest);
      return response.result;
    });

    // Resources handlers
    this.server.setRequestHandler(ListResourcesRequestSchema, async () => {
      const capabilities = this.capabilityAggregator.getCapabilities();
      return { resources: capabilities.resources };
    });

    this.server.setRequestHandler(ListResourceTemplatesRequestSchema, async () => {
      const capabilities = this.capabilityAggregator.getCapabilities();
      return { resourceTemplates: capabilities.resourceTemplates || [] };
    });

    this.server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
      // Convert to JSONRPCRequest format for router
      const jsonRpcRequest = {
        jsonrpc: '2.0' as const,
        id: Math.random().toString(36),
        method: 'resources/read',
        params: request.params
      };
      const response = await this.requestRouter.routeRequest(jsonRpcRequest);
      return response.result;
    });

    // Prompts handlers
    this.server.setRequestHandler(ListPromptsRequestSchema, async () => {
      const capabilities = this.capabilityAggregator.getCapabilities();
      return { prompts: capabilities.prompts };
    });

    this.server.setRequestHandler(GetPromptRequestSchema, async (request) => {
      // Convert to JSONRPCRequest format for router
      const jsonRpcRequest = {
        jsonrpc: '2.0' as const,
        id: Math.random().toString(36),
        method: 'prompts/get',
        params: request.params
      };
      const response = await this.requestRouter.routeRequest(jsonRpcRequest);
      return response.result;
    });

    logger.debug('All MCP request handlers set up');
  }

  /**
   * Get dynamic capabilities based on current backend connections
   */
  private getDynamicCapabilities(): any {
    const aggregatedCapabilities = this.capabilityAggregator.getCapabilities();

    return {
      tools: aggregatedCapabilities.tools.length > 0 ? {} : undefined,
      resources: aggregatedCapabilities.resources.length > 0 ? {} : undefined,
      prompts: aggregatedCapabilities.prompts.length > 0 ? {} : undefined
    };
  }

  /**
   * Update aggregated capabilities from all backend connections
   */
  private updateAggregatedCapabilities(): void {
    const connections = this.backendManager.getAllConnections();
    this.capabilityAggregator.updateCapabilities(connections);

    // Update server capabilities after aggregation
    this.updateServerCapabilities();
  }

  /**
   * Update server capabilities registration
   */
  private updateServerCapabilities(): void {
    try {
      const capabilities = this.getDynamicCapabilities();

      // Re-register capabilities with the server
      // Note: This is a workaround since MCP SDK doesn't support dynamic capability updates
      logger.debug('Server capabilities updated:', capabilities);
    } catch (error) {
      logger.error('Error updating server capabilities:', error);
    }
  }

  /**
   * Set up dynamic handlers based on aggregated capabilities
   */
  private setupDynamicHandlers(): void {
    // For now, we'll use a simpler approach with the low-level API
    // The actual request routing will be handled by the RequestRouter
    logger.debug('Dynamic handlers setup - using low-level API routing');
  }

  /**
   * Convert MCP prompt arguments to Zod schema
   */
  private convertArgsToSchema(args: any[]): Record<string, z.ZodType> {
    const schema: Record<string, z.ZodType> = {};
    for (const arg of args) {
      // Simple conversion - in practice this would be more sophisticated
      schema[arg.name] = z.string().optional();
    }
    return schema;
  }

  /**
   * Send capability change notifications
   */
  private async sendCapabilityChangeNotifications(): Promise<void> {
    try {
      // Update dynamic handlers when capabilities change
      this.setupDynamicHandlers();

      // The McpServer will automatically handle capability notifications
      logger.debug('Capability change notifications sent');
    } catch (error) {
      logger.error('Error sending capability change notifications:', error);
    }
  }

  /**
   * Forward notification to clients
   */
  private async forwardNotification(notification: any): Promise<void> {
    try {
      // Extract method and params from the notification
      const { method, params } = notification;

      if (!method) {
        logger.warn('Notification missing method, skipping:', notification);
        return;
      }

      // Only send notifications if the proxy is fully initialized
      if (!this.state.initialized) {
        logger.debug('Proxy not fully initialized, skipping notification:', method);
        return;
      }

      // Check if transport is connected before sending notification
      if (!this.server.transport) {
        logger.debug('No client transport connected, skipping notification:', method);
        return;
      }

      // Use the server's notification method with correct format
      await this.server.notification({ method, params });
      logger.debug('Notification forwarded to clients:', method);
    } catch (error) {
      if (error instanceof Error && error.message.includes('Not connected')) {
        logger.debug('No client connected for notification');
      } else {
        logger.error('Error forwarding notification:', error);
      }
    }
  }

  /**
   * Start the proxy server
   */
  async start(): Promise<void> {
    try {
      logger.info('Starting MCP Server Proxy...');

      // Connect to backend servers
      for (const serverConfig of this.config.servers) {
        if (serverConfig.enabled) {
          await this.backendManager.addServer(serverConfig);
        }
      }

      // Set up transport and connect
      const transport = new StdioServerTransport();
      await this.server.connect(transport);

      this.state.initialized = true;

      // Don't enable notification forwarding until a client connects
      // This prevents notifications from being sent to stdout during startup

      logger.info('MCP Server Proxy started successfully');

      this.emit('started');
    } catch (error) {
      logger.error('Failed to start MCP Server Proxy:', error);
      throw error;
    }
  }

  /**
   * Stop the proxy server
   */
  async stop(): Promise<void> {
    try {
      logger.info('Stopping MCP Server Proxy...');

      // Disconnect from backend servers
      await this.backendManager.disconnectAll();

      // Close server
      if (this.server) {
        await this.server.close();
      }

      this.state.initialized = false;
      logger.info('MCP Server Proxy stopped');

      this.emit('stopped');
    } catch (error) {
      logger.error('Error stopping MCP Server Proxy:', error);
      throw error;
    }
  }

  /**
   * Get proxy status
   */
  getStatus(): any {
    return {
      initialized: this.state.initialized,
      backendServers: this.backendManager.getAllConnections().map(conn => ({
        id: conn.id,
        connected: conn.connected,
        name: conn.config.name
      })),
      capabilities: this.capabilityAggregator.getCapabilities()
    };
  }
}