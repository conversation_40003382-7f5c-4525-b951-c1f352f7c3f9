/**
 * MCP Server Proxy - Main proxy server implementation
 */
import { EventEmitter } from 'events';
import { ProxyConfig } from '../types/index.js';
/**
 * Main MCP Server Proxy class
 */
export declare class MCPServerProxy extends EventEmitter {
    private server;
    private backendManager;
    private capabilityAggregator;
    private requestRouter;
    private notificationManager;
    private config;
    private state;
    private notificationDebounceTimer;
    private pendingNotifications;
    constructor(config: ProxyConfig);
    /**
     * Set up event handlers between managers
     */
    private setupEventHandlers;
    /**
     * Set up MCP server handlers using the low-level API
     */
    private setupServerHandlers;
    /**
     * Set up all MCP request handlers
     */
    private setupRequestHandlers;
    /**
     * Wait for the proxy to be fully initialized before handling requests
     */
    private waitForInitialization;
    /**
     * Get dynamic capabilities based on current backend connections
     */
    private getDynamicCapabilities;
    /**
     * Update aggregated capabilities from all backend connections
     */
    private updateAggregatedCapabilities;
    /**
     * Update server capabilities registration
     */
    private updateServerCapabilities;
    /**
     * Set up dynamic handlers based on aggregated capabilities
     */
    private setupDynamicHandlers;
    /**
     * Send capability change notifications with debouncing
     */
    private sendCapabilityChangeNotifications;
    /**
     * Send the actual notifications after debouncing
     */
    private sendDebouncedNotifications;
    /**
     * Forward notification to clients
     */
    private forwardNotification;
    /**
     * Start the proxy server
     */
    start(): Promise<void>;
    /**
     * Connect to all backend servers and wait for them to be ready
     */
    private connectAllBackendServers;
    /**
     * Wait for all capabilities to be loaded and aggregated
     */
    private waitForCapabilitiesReady;
    /**
     * Stop the proxy server
     */
    stop(): Promise<void>;
    /**
     * Get proxy status
     */
    getStatus(): any;
}
//# sourceMappingURL=MCPServerProxy.d.ts.map